package com.bobandata.cloud.trade.mvc.query;

import com.bobandata.cloud.trade.system.TradeSystemApplication;
import com.bobandata.cloud.trade.system.service.file.FileService;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/12/16 11:19
 * @Description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TradeSystemApplication.class})
public class MinioToSftp {

    @Resource
    private FileService fileService;
    @SneakyThrows
    @Test
    public void testMinioToSftp() {
        // 文件从minio - sftp
        // 从minio读取内容
        byte[] content = fileService.getFileContent(1L, "/upload/2024-12-12/675a9be25390037f19a18e8f/aa.txt");
        if (content == null) {
            throw new Exception("aa.txt" + "文件不存在");
        }
        // 上传到sftp中
        String path = fileService.createFile("我是从aa来的.txt", content, 2L);
        if (path == null) {
            throw new Exception("我是从aa来的.txt" + "文件上传失败");
        }
        System.out.println(path);
    }
}
