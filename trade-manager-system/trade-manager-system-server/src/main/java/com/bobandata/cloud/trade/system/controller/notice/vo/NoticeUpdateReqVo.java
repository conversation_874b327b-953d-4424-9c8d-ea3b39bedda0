package com.bobandata.cloud.trade.system.controller.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024-11-27日 14:18
 * @description
 */
@Schema(description = "系统公告 - 更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NoticeUpdateReqVo extends NoticeBaseVo {

    @Schema(description = "id", example = "1024", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "id不能为空")
    private Long id;
}
