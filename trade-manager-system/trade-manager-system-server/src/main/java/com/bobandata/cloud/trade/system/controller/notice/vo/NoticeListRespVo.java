package com.bobandata.cloud.trade.system.controller.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-11-27日 14:22
 * @description
 */
@Schema(description = "系统公告 - 列表 Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NoticeListRespVo extends NoticeBaseVo {

    @Schema(description = "id", example = "1024", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;
}
