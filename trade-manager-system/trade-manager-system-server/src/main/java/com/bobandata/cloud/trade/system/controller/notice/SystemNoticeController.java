package com.bobandata.cloud.trade.system.controller.notice;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.controller.notice.vo.NoticeCreateReqVo;
import com.bobandata.cloud.trade.system.controller.notice.vo.NoticeListReqVo;
import com.bobandata.cloud.trade.system.controller.notice.vo.NoticeListRespVo;
import com.bobandata.cloud.trade.system.controller.notice.vo.NoticeUpdateReqVo;
import com.bobandata.cloud.trade.system.service.common.SysNoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024-11-27日 13:56
 * @description
 */
@Tag(name = "公告管理")
@RequestMapping("/notice")
@RestController
@Validated
public class SystemNoticeController {

    @Autowired
    private SysNoticeService noticeService;

    @GetMapping("/list")
    @ResponseBody
    @Operation(description = "公告管理-列表查询")
    public PagingResult<NoticeListRespVo> listNotice(NoticeListReqVo listReqVo) {
        return noticeService.listNotice(listReqVo);
    }

    @PostMapping("/create")
    @ResponseBody
    @Operation(description = "公告管理-公告创建")
    public ServiceResult<String> createNotice(@Valid @RequestBody NoticeCreateReqVo createReqVo) {
        return noticeService.createNotice(createReqVo);
    }

    @PostMapping("/delete")
    @ResponseBody
    @Operation(description = "公告管理-公告删除")
    public ServiceResult<String> deleteNotice(Long id) {
        return noticeService.deleteNotice(id);
    }

    @PostMapping("/update")
    @ResponseBody
    @Operation(description = "公告管理-公告更新")
    public ServiceResult<String> updateNotice(@Valid @RequestBody NoticeUpdateReqVo updateReqVo) {
        return noticeService.updateNotice(updateReqVo);
    }
}

