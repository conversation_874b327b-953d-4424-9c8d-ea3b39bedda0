package com.bobandata.cloud.trade.system.controller.notice.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-11-27日 14:26
 * @description
 */
@Schema(description = "系统公告 - 列表 req vo")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NoticeListReqVo extends PageParam {

    @Schema(description = "标题模糊查询")
    private String title;
}
