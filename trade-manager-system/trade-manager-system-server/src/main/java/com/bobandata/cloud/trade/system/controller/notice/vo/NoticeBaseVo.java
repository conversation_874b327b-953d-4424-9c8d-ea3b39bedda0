package com.bobandata.cloud.trade.system.controller.notice.vo;

import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-11-27日 14:12
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class NoticeBaseVo {

    /**
     * 公告标题
     */
    @Schema(description = "公告标题")
    @NotBlank(message = "公告标题不能为空")
    @Size(max = 30, message = "公告标题长度不能超过30个字符")
    private String title;

    /**
     * 公告内容
     */
    @Schema(description = "公告内容")
    @NotBlank(message = "公告标题不能为空")
    private String content;

    /**
     * 公告状态（0正常 1关闭）
     */
    @Schema(description = "公告状态（0正常 1关闭）")
    private Integer status;

    /**
     * 公告权重
     */
    @Schema(description = "公告权重")
    private Integer level;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Timestamp expirationTime;

    /**
     * 公告的跳转链接
     */
    @Schema(description = "公告的跳转链接")
    private String linkInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Timestamp createTime;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Timestamp lastRefreshTime;
}
