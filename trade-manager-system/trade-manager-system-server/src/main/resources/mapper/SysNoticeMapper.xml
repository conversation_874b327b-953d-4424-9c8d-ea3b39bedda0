<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.system.dal.mysql.SysNoticeMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.system.dal.dataobject.SysNoticeDo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="level" column="level" jdbcType="INTEGER"/>
            <result property="expirationTime" column="expiration_time" jdbcType="TIMESTAMP"/>
            <result property="linkInfo" column="link_info" jdbcType="OTHER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
            <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
            <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,content,
        status,level,expiration_time,
        link_info,create_time,creator_id,
        last_refresh_time,last_modifier_id
    </sql>
</mapper>
